import React, { useState, useEffect } from 'react';
import { DateTime } from 'luxon';
import DButton from '../Global/DButton';
import CalendarIcon from '../Global/Icons/CalendarIcon';

const isValidDate = (dateStr) => /^\d{4}-\d{2}-\d{2}$/.test(dateStr);

const DDateRangePicker = ({ fromDate, toDate, onApply, className }) => {
  // Local state for input values
  const [localFrom, setLocalFrom] = useState(() => {
    return fromDate ? DateTime.fromISO(fromDate).toFormat('yyyy-MM-dd') : DateTime.now().toFormat('yyyy-MM-dd');
  });
  const [localTo, setLocalTo] = useState(() => {
    return toDate ? DateTime.fromISO(toDate).toFormat('yyyy-MM-dd') : DateTime.now().toFormat('yyyy-MM-dd');
  });

  // Keep local state in sync with parent props
  useEffect(() => {
    if (fromDate) {
      const formatted = DateTime.fromISO(fromDate).toFormat('yyyy-MM-dd');
      if (formatted !== localFrom) setLocalFrom(formatted);
    }
    // eslint-disable-next-line
  }, [fromDate]);
  useEffect(() => {
    if (toDate) {
      const formatted = DateTime.fromISO(toDate).toFormat('yyyy-MM-dd');
      if (formatted !== localTo) setLocalTo(formatted);
    }
    // eslint-disable-next-line
  }, [toDate]);

  const handleFromChange = (e) => {
    setLocalFrom(e.target.value);
  };

  const handleToChange = (e) => {
    setLocalTo(e.target.value);
  };

  const bothValid = isValidDate(localFrom) && isValidDate(localTo);
  const fromAfterTo = bothValid && DateTime.fromISO(localFrom) > DateTime.fromISO(localTo);
  const isApplyDisabled = !bothValid || fromAfterTo;

  const handleApply = () => {
    if (!isApplyDisabled) {
      onApply(localFrom, localTo);
    }
  };

  const defaultClasses = "flex flex-col space-y-4 sm:space-y-0 sm:flex-row sm:space-x-2 items-start";

  return (
    <div className={className || defaultClasses}>
      <div className='flex flex-col'>
        <div className='flex gap-size1'>
          <div className="flex flex-col w-full sm:w-auto">
            <div className="relative">
              <input
                type="date"
                id="from-date"
                value={localFrom}
                onChange={handleFromChange}
                onKeyDown={e => e.key === 'Enter' && e.preventDefault()}
                className="dbutton w-[124px] p-2 pl-8 border border-grey-5 rounded-md focus:outline-none bg-white hover:bg-grey-2 transition ease-in-out duration-150 text-sm"
              />
              <CalendarIcon className="absolute left-2 top-1/2 transform -translate-y-1/2 text-grey-50 w-4 h-4 pointer-events-none" />
            </div>
          </div>
          <div className="flex flex-col w-full sm:w-auto">
            <div className="relative">
              <input
                type="date"
                id="to-date"
                value={localTo}
                onChange={handleToChange}
                onKeyDown={e => e.key === 'Enter' && e.preventDefault()}
                className="dbutton w-[124px] p-2 pl-8 border border-grey-5 rounded-md focus:outline-none bg-white hover:bg-grey-2 transition ease-in-out duration-150 text-sm"
              />
              <CalendarIcon className="absolute left-2 top-1/2 transform -translate-y-1/2 text-grey-50 w-4 h-4 pointer-events-none" />
            </div>
          </div>
        </div>
        {fromAfterTo && (
          <span className="text-xs text-negative-100 ml-2">From date must be before To date</span>
        )}
      </div>
      <DButton
        type="button"
        onClick={handleApply}
        disabled={isApplyDisabled}
        variant='outlined'
        size='sm'
        className='!h-[40px] text-sm'
      >
        Apply
      </DButton>
    </div>
  );
};

export default DDateRangePicker;
